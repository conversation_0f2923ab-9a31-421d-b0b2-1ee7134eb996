package cn.jwis.product.pdm.customer.repo;

import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.platform.plm.container.entity.Folder;
import cn.jwis.platform.plm.container.entity.FolderTreeNode;
import cn.jwis.platform.plm.container.entity.Team;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.product.pdm.container.entity.response.PDMInstanceEntityWithCatalog;
import cn.jwis.product.pdm.customer.entity.FuzzySubPageWithModelDTO;

import java.util.List;
import java.util.Set;

public interface PdmFolderTwoRepo {

    List<FolderTreeNode> searchFoldersWithPermisson(String containerType, String containerModel, String containerOid, String searchKey) throws JWIException;


    List<Folder> findByParentOid(String parentOid);

    Team getTeam(String folderOid);

    Team getInstanceTeam(InstanceEntity instance);

    PageResult<PDMInstanceEntityWithCatalog> fuzzyFolderContentPageithModel(Set<String> strings, String searchKey, int index, int size, FuzzySubPageWithModelDTO dto);
}
