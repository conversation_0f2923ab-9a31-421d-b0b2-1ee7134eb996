package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.query.dynamic.ColumnUtil;
import cn.jwis.platform.plm.container.entity.EffectivityDefinition;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityService;
import cn.jwis.platform.plm.foundation.model.entity.EdgeDef;
import cn.jwis.platform.plm.foundation.model.entity.Property;
import cn.jwis.platform.plm.foundation.model.entity.VertexDef;
import cn.jwis.platform.plm.foundation.model.service.ModelHelper;
import cn.jwis.platform.plm.foundation.model.service.ModelService;
import cn.jwis.platform.plm.foundation.relationship.Use;
import cn.jwis.platform.plm.foundation.versionrule.able.info.LockInfo;
import cn.jwis.product.pdm.partbom.contianer.ContainerThirdParty;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import cn.jwis.product.pdm.partbom.part.service.PartService;
import cn.jwis.product.pdm.partbom.part.service.PartServiceImpl;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023/9/25 20:12
 * @Email <EMAIL>
 */
@Service
@Primary
@Transactional
public class CustomerPartService2Impl extends PartServiceImpl {
    private static final Logger log = LoggerFactory.getLogger(CustomerPartService2Impl.class);

    @Autowired
    CommonAbilityService commonAbilityService;

    @Autowired
    PartService partService;

    @Autowired
    private PartHelper partHelper;

    @Autowired
    private CommonAbilityHelper commonAbilityHelper;
    @Autowired
    private ContainerThirdParty containerThirdParty;
    @Autowired
    private ModelService modelService;
    @Autowired
    private ModelHelper modelHelper;


    String[] PART_BOM_HEADER = new String[]{"编码", "数量", "位号","位号1","行号"};
    private Map<String, String> linkMap = new HashMap(16);
    private Map<String, String> linkAttributeMap = new HashMap(16);
    private List<List<String>> dynamicHeaderList = new ArrayList();
    /*@Override
    public PartIteration batchAddBomParts(List<Map<Integer, String>> dataList,
                                          Map<Integer, String> headIndexMap, String topNumber) {
        String value;
        String headName;
        Set<String> excelNumbers = new HashSet<>();
        for (Map<Integer, String> dataMap : dataList) {
            for (Integer index : dataMap.keySet()) {
                value = dataMap.get(index);
                headName = headIndexMap.get(index);
                if (Constants.PARENT_NUMBER_HEADER.equals(headName) || Constants.CHILD_NUMBER_HEADER.equals(headName)) {
                    if(StringUtil.isNotEmpty(value)) {
                        excelNumbers.add(value);
                    }
                }
            }
        }
        StringBuilder error = new StringBuilder();
        if(CollectionUtil.isNotEmpty(excelNumbers)){
            List<PartIteration> tempData = partService.findLatestByNumber(excelNumbers);
            Set<String> dbNumbers = tempData.stream().map(d->d.getNumber()).collect(Collectors.toSet());
            excelNumbers.removeAll(dbNumbers);
            Assert.isEmpty(excelNumbers, JSON.toJSONString(excelNumbers) + " 不存在，无法构造BOM！");
        }
        return super.batchAddBomParts(dataList, headIndexMap, topNumber);
    }*/


    @Override
    @Transactional
    public PartIteration batchAddBomParts(List<Map<Integer, String>> dataList, Map<Integer, String> headIndexMap, String topNumber) {
        PartIteration topCheckInPart = null;
        log.info("{}条数据，开始存储数据库！", dataList.size());
        log.info("列表头数据如下:{}！", JSONUtil.toJsonStr(headIndexMap));

        // 只有当表头包含“位号1”时，才执行合并
        mergePosition(dataList, headIndexMap);

        StringBuilder error = new StringBuilder();

        // Step 1: 校验每行数据的编码、数量、位号,重复的位号，位号和数量不匹配的编码
        StringBuilder errorBuilder = validateBOMData(dataList, headIndexMap);

        // 抛出统一异常
        if (errorBuilder.length() > 0) {
            throw new JWIException("BOM 校验失败:\n" + errorBuilder.toString());
        }

        log.info("BOM 校验通过，开始执行后续逻辑");

        dataList = mergeDuplicateBomEntries(dataList, headIndexMap);

        // Step 2: 初始化 BOM 映射信息
        Map<String, List<String>> partBomMap = new HashMap<>(16);
        Map<String, Double> bomQuantityMap = new HashMap<>(16);
        Map<String, String> bomUnitMap = new HashMap<>(16);
        Map<String, JSONObject> extendAttrMap = new HashMap<>(16);

        // Step 3: 处理数据并生成 BOM 相关信息
        for (Map<Integer, String> dataMap : dataList) {
            String useAttrQuantity = ColumnUtil.getFieldName(Use::getQuantity);
            String useAttrUnit = ColumnUtil.getFieldName(Use::getUnit);
            String childPartNumber = null;
            String parentPartNumber = topNumber;

            for (Integer index : dataMap.keySet()) {
                String value = dataMap.get(index);
                String headName = headIndexMap.get(index);

                // 处理编码本行数据
                if ("编码".equals(headName)) {
                    childPartNumber = value;
                    // 使用 `computeIfAbsent` 确保不会覆盖之前的数据
                    List<String> childPartList = partBomMap.computeIfAbsent(topNumber, k -> new ArrayList<>());
                    childPartList.add(value);
                }

                // 处理数量、单位和扩展属性
                String countCode = linkMap.get(headName);
                if (StringUtils.isNotEmpty(countCode)) {
                    if (countCode.toLowerCase().equals(useAttrQuantity)) {
                        bomQuantityMap.put(parentPartNumber + ";;;" + childPartNumber, StringUtils.isEmpty(value) ? 1.0 : Double.valueOf(value));
                    } else if (countCode.toLowerCase().equals(useAttrUnit)) {
                        bomUnitMap.put(parentPartNumber + ";;;" + childPartNumber, value);
                    } else {
                        JSONObject jsonObject = extendAttrMap.get(parentPartNumber + ";;;" + childPartNumber);
                        if (jsonObject == null) {
                            jsonObject = new JSONObject();
                        }
                        jsonObject.put(countCode, value);
                        extendAttrMap.put(parentPartNumber + ";;;" + childPartNumber, jsonObject);
                    }
                }
            }
        }

        // Step 4: 查询所有部件
        List<String> codes = new ArrayList<>(partBomMap.keySet());
        List<String> parentCodes = new ArrayList<>(partBomMap.keySet());
        for (String parentCode : partBomMap.keySet()) {
            codes.addAll(partBomMap.get(parentCode));
        }
        codes = codes.stream().distinct().collect(Collectors.toList());

        List<PartIteration> allParts = partService.findByCodesAndCatalogOid(codes);
        checkDuplicateNumber(codes, allParts);

        // Step 5: 检查部件是否被锁定，并处理检出操作
        Map<String, PartIteration> partIterationMap = allParts.stream().collect(Collectors.toMap(PartIteration::getNumber, Function.identity(), (key1, key2) -> key2));
        Map<String, PartIteration> checkoutPartIterationMap = new HashMap<>(16);
        String parentPartNumber = SessionHelper.getCurrentUser().getOid();

        for (String number : parentCodes) {
            PartIteration partIteration = partIterationMap.get(number);
            ModelInfo modelInfo = new ModelInfo();
            modelInfo.setType(partIteration.getType());
            modelInfo.setOid(partIteration.getOid());

            if (StringUtils.isEmpty(partIteration.getLockOwnerOid())) {
                PartIteration checkoutPart = (PartIteration) partHelper.checkOut(modelInfo);
                partIterationMap.put(number, checkoutPart);
                checkoutPartIterationMap.put(number, checkoutPart);
            } else if (parentPartNumber.equals(partIteration.getLockOwnerOid())) {
                partIterationMap.put(number, partIteration);
                checkoutPartIterationMap.put(number, partIteration);
            } else {
                error.append("编码:").append(number).append("，被人员：").append(partIteration.getLockOwnerAccount()).append("已经检出, 您无法操作这条编码!");
            }
        }

        // Step 6: 如果有错误，抛出异常
        if (error.length() > 0) {
            Assert.isEmpty(error.toString(), error.toString());
        }

        // Step 7: 处理 Use 对象，保存 BOM 使用关系
        List<Use> useList = new ArrayList<>();
        List<Use> dbUseList = new ArrayList<>();

        for (String number : partBomMap.keySet()) {
            List<String> childPartList1 = partBomMap.get(number);
            if (CollectionUtils.isEmpty(childPartList1)) continue;

            for (String childNumber : childPartList1) {
                String code = number + ";;;" + childNumber;
                Use use = new Use();
                use.setFromOid(partIterationMap.get(number).getOid());
                use.setFromType(partIterationMap.get(number).getType());
                use.setToOid(partIterationMap.get(childNumber).getOid());
                use.setToType(partIterationMap.get(childNumber).getType());
                use.setQuantity(bomQuantityMap.get(code) == null ? 1.0 : bomQuantityMap.get(code));
                use.setUnit(bomUnitMap.get(code));

                if (CollUtil.isNotEmpty(extendAttrMap) && CollUtil.isNotEmpty(extendAttrMap.get(code))) {
                    use.setExtensionContent(extendAttrMap.get(code));
                }

                useList.add(use);

                List<Use> uses = partService.findUseByFromToNoTenantOid(partIterationMap.get(number).getOid(), use.getToOid());
                if (CollUtil.isNotEmpty(uses)) {
                    dbUseList.addAll(uses);
                }
            }
        }

        // Step 8: 删除已有的 Use 记录，添加新的 Use 记录
        if (!CollectionUtils.isEmpty(dbUseList)) {
            commonAbilityHelper.deleteUse(dbUseList);
        }

        // Step 9:  删除原来部件下所有bom
        List<Use> uses = partService.findUseByFromToNoTenantOid(partIterationMap.get(topNumber).getOid(), "");
        log.info("即将删除 {} 条旧 BOM 记录: {}", uses.size(), JSONUtil.toJsonStr(uses));
        if (!CollectionUtils.isEmpty(uses)) {
            commonAbilityHelper.deleteUse(uses);
        }


        if (!CollectionUtils.isEmpty(useList)) {
            log.info("use count==>{}", useList.size());
            partHelper.addUse(useList);

            // Step 10: 完成检入操作
            for (String number : checkoutPartIterationMap.keySet()) {
                PartIteration needCheckinPart = checkoutPartIterationMap.get(number);
                LockInfo lockInfo = new LockInfo();
                lockInfo.setOid(needCheckinPart.getOid());
                lockInfo.setType(needCheckinPart.getType());
                partHelper.checkIn(lockInfo);

                if (topNumber.equals(number)) {
                    topCheckInPart = needCheckinPart;
                }
            }

            log.info("part check in complete!");
        }

        return topCheckInPart;
    }

    /**
     * 批量更新指定OID部件的BOM数据
     * 该方法结合prepareBomDataForOid的数据准备逻辑和BOM变更处理逻辑
     *
     * @param dataList 导入的BOM数据
     * @param headIndexMap 表头映射
     * @param partOid 部件OID
     * @return 更新后的部件
     */
    @Transactional
    public PartIteration batchUpdateBomForPartOid(List<Map<Integer, String>> dataList, Map<Integer, String> headIndexMap, String partOid) {
        log.info("开始为OID={}的部件更新BOM数据", partOid);
        
        // 获取部件信息
        PartIteration partIteration = partService.findByOid(partOid);
        if (partIteration == null) {
            throw new JWIException("未找到OID为" + partOid + "的部件");
        }
        
        // 准备BOM数据
        List<Use> newUseList = prepareBomDataForOid(dataList, headIndexMap, partOid);

        log.info("当前导入BOM数据：{}", JSONUtil.toJsonStr(newUseList));

        // 获取部件当前的所有BOM引用
        List<Use> oldUseList = partService.findUseByFromToNoTenantOid(partOid, "");
        log.info("部件当前有{}条BOM记录", oldUseList.size());

        try {
            log.info("即将删除 {} 条旧 BOM 记录: {}", oldUseList.size(), JSONUtil.toJsonStr(oldUseList));
            if (!CollectionUtils.isEmpty(oldUseList)) {
                //这里只删除变更对象中的 BOM,
                commonAbilityService.deleteUse(oldUseList);
            }
            
            // 添加新的BOM关系
            if (!CollectionUtils.isEmpty(newUseList)) {
                log.info("添加{}条新的BOM引用", newUseList.size());
                partHelper.batchAddChangeUse(newUseList);
            }

            log.info("部件BOM更新完成");
            return partIteration;
        } catch (Exception e) {
            // 发生异常时，如果已检出则取消检出
            throw new JWIException("更新BOM失败: " + e.getMessage(), e);
        }
    }

    /**
     * 准备BOM数据，用于处理指定OID部件的BOM变更
     * 复用batchAddBomParts的校验和数据处理逻辑，但不执行检出/检入操作
     *
     * @param dataList 导入的BOM数据
     * @param headIndexMap 表头映射
     * @param partOid 部件OID
     * @return 构建好的Use对象列表
     */
    public List<Use> prepareBomDataForOid(List<Map<Integer, String>> dataList, Map<Integer, String> headIndexMap, String partOid) {
        log.info("{}条数据，开始为OID={}的部件准备BOM数据", dataList.size(), partOid);
        log.info("列表头数据如下 :{}！", JSONUtil.toJsonStr(headIndexMap));
        
        // 只有当表头包含"位号1"时，才执行合并
        mergePosition(dataList, headIndexMap);

        // Step 1: 校验每行数据的编码、数量、位号,重复的位号，位号和数量不匹配的编码
        StringBuilder errorBuilder = validateBOMData(dataList, headIndexMap);

        // 抛出统一异常
        if (errorBuilder.length() > 0) {
            throw new JWIException("BOM 校验失败:\n" + errorBuilder.toString());
        }

        log.info("BOM校验通过 ，开始执行后续逻辑");
        
        // 合并重复BOM条目
        dataList = mergeDuplicateBomEntries(dataList, headIndexMap);

        // Step 2: 获取部件信息
        PartIteration partIteration = partService.findByOid(partOid);
        if (partIteration == null) {
            throw new JWIException("未找到OID为" + partOid + "的部件");
        }
        
        String topNumber = partIteration.getNumber();
        
        // Step 3: 初始化 BOM 映射信息
        Map<String, List<String>> partBomMap = new HashMap<>(16);
        Map<String, Double> bomQuantityMap = new HashMap<>(16);
        Map<String, String> bomUnitMap = new HashMap<>(16);
        Map<String, JSONObject> extendAttrMap = new HashMap<>(16);

        // Step 4: 处理数据并生成 BOM 相关信息
        for (Map<Integer, String> dataMap : dataList) {
            String useAttrQuantity = ColumnUtil.getFieldName(Use::getQuantity);
            String useAttrUnit = ColumnUtil.getFieldName(Use::getUnit);
            String childPartNumber = null;
            String parentPartNumber = topNumber;

            for (Integer index : dataMap.keySet()) {
                String value = dataMap.get(index);
                String headName = headIndexMap.get(index);

                // 处理编码本行数据
                if ("编码".equals(headName)) {
                    childPartNumber = value;
                    // 使用 `computeIfAbsent` 确保不会覆盖之前的数据
                    List<String> childPartList = partBomMap.computeIfAbsent(topNumber, k -> new ArrayList<>());
                    childPartList.add(value);
                }

                // 处理数量、单位和扩展属性
                String countCode = linkMap.get(headName);
                if (StringUtils.isNotEmpty(countCode)) {
                    if (countCode.toLowerCase().equals(useAttrQuantity)) {
                        bomQuantityMap.put(parentPartNumber + ";;;" + childPartNumber, StringUtils.isEmpty(value) ? 1.0 : Double.valueOf(value));
                    } else if (countCode.toLowerCase().equals(useAttrUnit)) {
                        bomUnitMap.put(parentPartNumber + ";;;" + childPartNumber, value);
                    } else {
                        JSONObject jsonObject = extendAttrMap.get(parentPartNumber + ";;;" + childPartNumber);
                        if (jsonObject == null) {
                            jsonObject = new JSONObject();
                        }
                        jsonObject.put(countCode, value);
                        extendAttrMap.put(parentPartNumber + ";;;" + childPartNumber, jsonObject);
                    }
                }
            }
        }

        // Step 5: 查询所有部件
        List<String> codes = new ArrayList<>(partBomMap.keySet());
        List<String> childCodes = new ArrayList<>();
        for (String parentCode : partBomMap.keySet()) {
            childCodes.addAll(partBomMap.get(parentCode));
        }
        codes.addAll(childCodes);
        codes = codes.stream().distinct().collect(Collectors.toList());

        List<PartIteration> allParts = partService.findByCodesAndCatalogOid(codes);
        checkDuplicateNumber(codes, allParts);

        // Step 6: 构建部件映射
        Map<String, PartIteration> partIterationMap = allParts.stream()
                .collect(Collectors.toMap(PartIteration::getNumber, Function.identity(), (key1, key2) -> key2));

        // Step 7: 构建Use列表
        List<Use> useList = new ArrayList<>();

        for (String number : partBomMap.keySet()) {
            List<String> childPartList = partBomMap.get(number);
            if (CollectionUtils.isEmpty(childPartList)) continue;

            for (String childNumber : childPartList) {
                String code = number + ";;;" + childNumber;
                Use use = new Use();
                use.setFromOid(partOid);
                use.setFromType(partIterationMap.get(number).getType());
                use.setToOid(partIterationMap.get(childNumber).getOid());
                use.setToType(partIterationMap.get(childNumber).getType());
                use.setQuantity(bomQuantityMap.get(code) == null ? 1.0 : bomQuantityMap.get(code));
                use.setUnit(bomUnitMap.get(code));

                if (CollUtil.isNotEmpty(extendAttrMap) && CollUtil.isNotEmpty(extendAttrMap.get(code))) {
                    use.setExtensionContent(extendAttrMap.get(code));
                }

                useList.add(use);
            }
        }

        log.info("为OID={}的部件准备了{}条BOM数据", partOid, useList.size());
        return useList;
    }

    private static void mergePosition(List<Map<Integer, String>> dataList, Map<Integer, String> headIndexMap) {
        if (headIndexMap.containsValue("位号1")) {
            log.info("开始合并位号与位号1字段，共{}行数据", dataList.size());
            for (int i = 0; i < dataList.size(); i++) {
                Map<Integer, String> row = dataList.get(i);
                String pos = row.get(2);  // 位号列
                String pos1 = row.get(3); // 位号1列

                log.debug("第{}行原始位号: [{}], 位号1: [{}]", i + 1, pos, pos1);

                Set<String> mergedPositions = new LinkedHashSet<>();
                if (StringUtil.isNotEmpty(pos)) {
                    mergedPositions.addAll(Arrays.stream(pos.split("[,，\\s]+"))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .collect(Collectors.toList()));
                }
                if (StringUtil.isNotEmpty(pos1)) {
                    mergedPositions.addAll(Arrays.stream(pos1.split("[,，\\s]+"))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .collect(Collectors.toList()));
                }

                String joined = String.join(", ", mergedPositions);
                row.put(2, joined);

                log.debug("第{}行合并后的位号: [{}]", i + 1, joined);
            }
            log.info("位号合并完成");
        } else {
            log.info("未发现“位号1”字段，跳过位号合并逻辑");
        }
    }

    private static StringBuilder validateBOMData(List<Map<Integer, String>> dataList, Map<Integer, String> headIndexMap) {
        Set<String> duplicatePositionSet = new HashSet<>(); // 重复的位号（去重）
        Set<String> mismatchPartNoSet = new HashSet<>(); // 数量和位号数不匹配的编码（去重）

        Set<String> allPositionSet = new HashSet<>();
        Map<String, String> positionToPartNoMap = new HashMap<>(); // 位号 -> 编码

        Integer codeIndex = null, quantityIndex = null, positionIndex = null;

        // 初始化字段索引
        for (Map.Entry<Integer, String> entry : headIndexMap.entrySet()) {
            if ("编码".equals(entry.getValue())) codeIndex = entry.getKey();
            if ("数量".equals(entry.getValue())) quantityIndex = entry.getKey();
            if ("位号".equals(entry.getValue())) positionIndex = entry.getKey();
        }

        int rowIndex = 1;
        for (Map<Integer, String> row : dataList) {
            String partNo = codeIndex != null ? row.get(codeIndex) : null;
            String quantityStr = quantityIndex != null ? row.get(quantityIndex) : null;
            String positionStr = positionIndex != null ? row.get(positionIndex) : null;

            // 位号和数量匹配校验
            // 位号和数量匹配校验（兼容小数判断）
            if (StringUtils.isNotEmpty(quantityStr) && StringUtils.isNotEmpty(positionStr)) {
                try {
                    BigDecimal qtyDecimal = new BigDecimal(quantityStr.trim());
                    int quantity = qtyDecimal.intValueExact(); // 只能是整数，否则抛出 ArithmeticException

                    int referenceCount = positionStr.split("[,，]").length;
                    if (quantity != referenceCount) {
                        if (StringUtils.isNotEmpty(partNo)) {
                            mismatchPartNoSet.add(partNo.trim());
                        }
                        log.warn("位号数量不匹配：料号 [{}]，数量 [{}]，位号数 [{}]]",
                                partNo, quantity, referenceCount);
                    }
                } catch (NumberFormatException | ArithmeticException e) {
                    // 数量格式不对（非数字或小数），加入错误列表
                    mismatchPartNoSet.add(partNo != null ? partNo.trim() : "行" + rowIndex);
                    log.warn("数量格式异常：料号 [{}]，数量字段 [{}]，错误信息：{}", partNo, quantityStr, e.getMessage());
                }
            }

            // 位号重复校验
            if (StringUtils.isNotEmpty(positionStr)) {
                String[] positions = positionStr.split("[,，]");
                for (String pos : positions) {
                    pos = pos.trim();
                    if (StringUtils.isEmpty(pos)) continue;

                    if (!allPositionSet.add(pos)) {
                        duplicatePositionSet.add(pos);
                    } else {
                        positionToPartNoMap.put(pos, partNo);
                    }
                }
            }

            rowIndex++;
        }

        // 构造错误信息
        StringBuilder errorBuilder = new StringBuilder();

        if (!mismatchPartNoSet.isEmpty()) {
            errorBuilder.append("位号与数量不匹配：")
                    .append(String.join("，", mismatchPartNoSet))
                    .append("\n");
        }

        if (!duplicatePositionSet.isEmpty()) {
            errorBuilder.append("位号重复: ")
                    .append(String.join("，", duplicatePositionSet))
                    .append("\n");
        }
        return errorBuilder;
    }

    @Override
    public List<List<String>> queryPartBomAttributes(String partCode, String containerOid) {
        // 初始化表头列表
        List<List<String>> headerList = new ArrayList<>();

        // 添加固定的BOM表头
        for (String header : PART_BOM_HEADER) {
            List<String> fixHeadList = new ArrayList<>();
            fixHeadList.add(header);
            headerList.add(fixHeadList);
        }

        // 如果 containerOid 存在，添加是否配置项和对应的配置属性
        if (StringUtils.isNotEmpty(containerOid)) {
            headerList.add(Collections.singletonList("是否配置项"));
            List<EffectivityDefinition> effConfig = containerThirdParty.findByContainerOid(containerOid);
            for (EffectivityDefinition eff : effConfig) {
                headerList.add(Collections.singletonList(eff.getName()));
            }
        }

        // 获取零件的模型结构
        List<VertexDef> partDefs = modelService.flapTree(partCode);
        if (CollectionUtils.isEmpty(partDefs)) {
            throw new JWIException(partCode + " not model!");
        }

        // 获取零件的 OID，并查找 USE 关系的边
        String fromOid = partDefs.get(0).getOid();
        List<EdgeDef> defList = modelHelper.findEdgeBetweenNode(fromOid, fromOid);
        if (CollectionUtils.isEmpty(defList)) {
            throw new JWIException(partCode + " has not USE relation !");
        }

        // 遍历 USE 关系，添加动态属性
        for (EdgeDef edgeDef : defList) {
            List<Property> properties = edgeDef.getProperties();
            if (CollUtil.isNotEmpty(properties)) {
                for (Property property : properties) {
                    String description = property.getDescription();
                    String code = property.getCode();
                    linkMap.put(description, code);
                    linkAttributeMap.put(code.toLowerCase(), description);
//                    headerList.add(Collections.singletonList(description));
                }
            }
        }

        // 更新动态表头缓存
        this.dynamicHeaderList = headerList;
        return headerList;
    }


    private static void checkDuplicateNumber(List<String> codes, List<PartIteration> allParts) {
        // 如果数据库中的部件数与输入的部件数不匹配
        if (allParts.size() != codes.size()) {
            // 计算哪些部件编码在数据库中不存在
            List<String> nonExistentList = new ArrayList<>(codes);
            List<String> dbPartCodes = allParts.stream()
                    .map(PartIteration::getNumber)
                    .collect(Collectors.toList());
            nonExistentList.removeAll(dbPartCodes);

            // 如果有不存在的部件编码，抛出异常
            if (!nonExistentList.isEmpty()) {
                throw new JWIException(String.join(",", nonExistentList) + " 物料不存在，请先申请物料");
            }

            // 统计部件编码的出现次数
            Map<String, Integer> duplicateMap = new HashMap<>();
            for (String partCode : dbPartCodes) {
                duplicateMap.put(partCode, duplicateMap.getOrDefault(partCode, 0) + 1);
            }

            // 找出重复的部件编码
            List<String> duplicateNumberList = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : duplicateMap.entrySet()) {
                if (entry.getValue() > 1) {
                    duplicateNumberList.add(entry.getKey());
                }
            }

            // 如果有重复的部件编码，抛出异常
            if (!duplicateNumberList.isEmpty()) {
                throw new JWIException("存在重复的部件编码: " + JSON.toJSONString(duplicateNumberList));
            }
        }
    }

    public List<Map<Integer, String>> mergeDuplicateBomEntries(List<Map<Integer, String>> dataList, Map<Integer, String> headIndexMap) {
        // 结果集合
        Map<String, Map<Integer, String>> mergedDataMap = new LinkedHashMap<>();

        for (Map<Integer, String> dataMap : dataList) {
            String partNumber = null;
            String quantity = "0";
            String position = "";

            // 先找到编码、数量、位号
            for (Integer index : dataMap.keySet()) {
                String value = dataMap.get(index);
                String headName = headIndexMap.get(index);

                if ("编码".equals(headName)) {
                    partNumber = value;
                } else if ("数量".equals(headName)) {
                    quantity = StringUtils.isEmpty(value) ? "0" : value;
                } else if ("位号".equals(headName)) {
                    position = value;
                }
            }

            if (partNumber != null) {
                // 合并逻辑
                Map<Integer, String> existingMap = mergedDataMap.get(partNumber);
                if (existingMap != null) {
                    for (Integer index : dataMap.keySet()) {
                        String headName = headIndexMap.get(index);
                        String existingValue = existingMap.get(index);
                        String newValue = dataMap.get(index);

                        if ("数量".equals(headName)) {
                            double totalQuantity = Double.parseDouble(existingValue) + Double.parseDouble(newValue);
                            existingMap.put(index, String.valueOf(totalQuantity));
                        } else if ("位号".equals(headName)) {
                            existingMap.put(index, mergePositions(existingValue, newValue));
                        }
                    }
                } else {
                    mergedDataMap.put(partNumber, new HashMap<>(dataMap));
                }
            }
        }

        return new ArrayList<>(mergedDataMap.values());
    }

    private String mergePositions(String existing, String incoming) {
        if (StringUtils.isEmpty(existing)) return incoming;
        if (StringUtils.isEmpty(incoming)) return existing;

        // 使用 LinkedHashSet 去重，并保持顺序
        Set<String> positionSet = new LinkedHashSet<>(Arrays.asList(existing.split(",\\s*")));
        positionSet.addAll(Arrays.asList(incoming.split(",\\s*")));

        // 拼接成 "1, 2, 3" 格式
        return String.join(", ", positionSet);
    }
    private void validateBomQuantityAndReferences(List<Map<Integer, String>> dataList, Map<Integer, String> headIndexMap) {
        int rowIndex = 1;
        StringBuilder error = new StringBuilder();

        for (Map<Integer, String> dataMap : dataList) {
            Integer quantityIndex = null;
            Integer referenceIndex = null;

            // 找出数量和位号的索引
            for (Map.Entry<Integer, String> entry : headIndexMap.entrySet()) {
                if ("数量".equals(entry.getValue())) {
                    quantityIndex = entry.getKey();
                }
                if ("位号".equals(entry.getValue())) {
                    referenceIndex = entry.getKey();
                }
            }

            // 如果没有数量或位号字段，跳过
            if (quantityIndex == null || referenceIndex == null) {
                rowIndex++;
                continue;
            }

            String quantityStr = dataMap.get(quantityIndex);
            String referenceStr = dataMap.get(referenceIndex);

            if (StringUtils.isNotEmpty(quantityStr) && StringUtils.isNotEmpty(referenceStr)) {
                try {
                    int quantity = Integer.parseInt(quantityStr);
                    int referenceCount = referenceStr.split(",\\s*").length;
                    if (quantity != referenceCount) {
                        error.append("第").append(rowIndex).append("行: 数量和位号数量不一致! 数量=").append(quantity)
                                .append("，位号数=").append(referenceCount).append("\n");
                    }
                } catch (NumberFormatException e) {
                    error.append("第").append(rowIndex).append("行: 数量字段格式错误!\n");
                }
            }
            rowIndex++;
        }

        // 如果有错误，抛出异常
        if (error.length() > 0) {
            throw new IllegalArgumentException("BOM 数量和位号校验失败:\n" + error);
        }
    }




}
