package cn.jwis.product.pdm.customer.repo.neo4j;

import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.response.PageResult;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.MapUtil;
import cn.jwis.framework.base.util.StringUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.SubPageFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.framework.database.core.query.dynamic.Order;
import cn.jwis.framework.database.neo4j.condition.Neo4jEnableConditional;
import cn.jwis.framework.database.neo4j.execution.CommonEntityTemplate;
import cn.jwis.framework.database.neo4j.util.ConditionParserNeo4j;
import cn.jwis.framework.database.neo4j.util.JSONUtil;
import cn.jwis.framework.database.neo4j.util.OrderParserNeo4j;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.container.FolderRepo;
import cn.jwis.platform.plm.container.entity.*;
import cn.jwis.platform.plm.container.entity.response.FolderForExport;
import cn.jwis.platform.plm.container.entity.response.InstanceEntityWithCatalog;
import cn.jwis.platform.plm.foundation.classification.entity.Classification;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.extendedproperty.entity.PropertyGroup;
import cn.jwis.platform.plm.foundation.relationship.Assign;
import cn.jwis.platform.plm.foundation.relationship.Attribute;
import cn.jwis.platform.plm.foundation.relationship.Contain;
import cn.jwis.product.pdm.customer.entity.FuzzyPageVO;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.neo4j.driver.Record;
import org.neo4j.driver.Result;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Component
@Conditional(Neo4jEnableConditional.class)
@Primary
public class CustomFolderRepoImpl extends CommonEntityTemplate<Folder> implements FolderRepo {


    @Override
    public Long deleteFolder(List<String> oidList) throws JWIException {
        Map<String, Object> params = new HashMap();
        params.put("oidList", oidList);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match (f:Folder) where f.oid in $oidList detach delete f return count(f) as cnt");
        return this.excuteCount(cypher.toString(), params, "cnt");
    }

    @Override
    public Long cleanDomain(List<String> oidList) {
        Map<String, Object> params = new HashMap();
        params.put("oidList", oidList);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match (f:Folder)-[:POINT]->(a:AdministrativeDomain) where f.oid in $oidList detach delete a return count(a) as cnt");
        return this.excuteCount(cypher.toString(), params, "cnt");
    }

    @Override
    public Long cleanTeam(List<String> oidList) {
        Map<String, Object> params = new HashMap();
        params.put("oidList", oidList);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match (f:Folder)-[:ASSIGN]->(t:Team) where f.oid in $oidList detach delete t return count(t) as cnt");
        return this.excuteCount(cypher.toString(), params, "cnt");
    }

    @Override
    public boolean hasContainOther(String oid) {
        Map<String, List<String>> result = this.queryContainChild(oid);
        return result.size() != 1 || !StringUtils.equalsAnyIgnoreCase((CharSequence)result.keySet().stream().findAny().get(), new CharSequence[]{"Folder"});
    }

    @Override
    public Map<String, List<String>> queryContainChild(String oid) {
        Map<String, Object> params = new HashMap();
        params.put("oid", oid);
        StringBuffer cypher = new StringBuffer();
        cypher.append("match (f:Folder)-[:CONTAIN*0..]->(c) where f.oid =$oid return c.type as type,c.oid as oid");
        List<Map<String, Object>> list = this.excuteMultiMap(cypher.toString(), params);
        Map<String, List<String>> result = new HashMap();
        list.forEach((item) -> {
            ((List)result.computeIfAbsent(item.getOrDefault("type", "").toString(), (k) -> {
                return new ArrayList();
            })).add(item.getOrDefault("oid", "").toString());
        });
        return result;
    }

    @Override
    public PageResult<InstanceEntityWithCatalog> fuzzyFolderContentPage(Collection<String> folderOids, String searchKey, int index, int size, List<String> subTypes) {
        searchKey = this.transfer(searchKey);
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("userOid", SessionHelper.getCurrentUser().getOid());
        cypher.append(" call db.index.fulltext.queryNodes('content_management', ").append(" ' (catalogOid:").append(String.join(" OR catalogOid:", folderOids)).append(")' ");
        if (CollectionUtil.isNotEmpty(subTypes)) {
            cypher.append(" + ' AND (type:").append(String.join(" OR type:", subTypes)).append(")' ");
        }

        if (StringUtil.isNotBlank(searchKey)) {
            searchKey = "*" + searchKey + "*";
            cypher.append(" + ' AND ( name:").append(searchKey).append(" OR number:").append(searchKey).append(" )' ");
        }

        cypher.append(") yield node as s where s.latest is null or (s.latest=true and ( ").append(" (s.lockOwnerOid is null )").append(" or (s.lockOwnerOid=$userOid and s.lockSourceOid is not null ) ").append(" or (s.lockOwnerOid<>$userOid and s.lockSourceOid is null ))) with s ");
        StringBuffer cntCypher = new StringBuffer(cypher);
        cntCypher.append(" return count(s) as cnt ");
        cypher.append(" return s order by s.updateDate desc, s.name ").append(this.dealPage(size, index));
        List<InstanceEntityWithCatalog> list = this.excuteList(cypher.toString(), map, "s", InstanceEntityWithCatalog.class);
        Long cnt = this.excuteCount(cntCypher.toString(), map, "cnt");
        return PageResult.init(cnt.intValue(), index, size, list);
    }

    @Override
    public PageResult<InstanceEntityWithCatalog> fuzzyFolderContentPage(FuzzyElectronPageVO pageVO) {
        String searchKey = pageVO.getSearchKey();
        List<String> folderOids = pageVO.getFolderOids();
        List<String> subTypes = pageVO.getSubTypes();
        int index = pageVO.getIndex();
        int size = pageVO.getSize();
        String preferenceLevel = pageVO.getPreferenceLevel();
        String supplierOid = pageVO.getSupplierOid();
        searchKey = this.transfer(searchKey);
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("userOid", SessionHelper.getCurrentUser().getOid());
        cypher.append(" call db.index.fulltext.queryNodes('content_management', ").append(" ' (catalogOid:").append(String.join(" OR catalogOid:", folderOids)).append(")' ");
        if (CollectionUtil.isNotEmpty(subTypes)) {
            cypher.append(" + ' AND (type:").append(String.join(" OR type:", subTypes)).append(")' ");
        }

        if (StringUtil.isNotBlank(searchKey)) {
            searchKey = "*" + searchKey + "*";
            cypher.append(" + ' AND ( name:").append(searchKey).append(" OR number:").append(searchKey).append(" )' ");
        }

        cypher.append(") yield node as s where s.latest is null or (s.latest=true and ( ").append(" (s.lockOwnerOid is null )").append(" or (s.lockOwnerOid=$userOid and s.lockSourceOid is not null ) ").append(" or (s.lockOwnerOid<>$userOid and s.lockSourceOid is null ))) ");
        if (StringUtil.isNotBlank(preferenceLevel)) {
            map.put("preferenceLevel", preferenceLevel);
            cypher.append(" AND ( s.preferenceLevel=$preferenceLevel ) ");
        }

        if (StringUtil.isNotBlank(preferenceLevel)) {
            map.put("preferenceLevel", preferenceLevel);
            cypher.append(" AND ( s.preferenceLevel=$preferenceLevel ) ");
        }

        String modelDefinition = pageVO.getModelDefinition();
        if (StringUtil.isNotBlank(modelDefinition)) {
            map.put("modelDefinition", modelDefinition);
            cypher.append(" AND ( s.modelDefinition=$modelDefinition ) ");
        }

        if (StringUtil.isNotBlank(supplierOid)) {
            map.put("supplierOid", supplierOid);
            cypher.append(" with s match (s)<-[it:ITERATE]-(m)-[r:CONTAIN]->(su:SupplierPart) where su.manufacturer=$supplierOid  ");
        }

        cypher.append(" with s ");
        cypher.append(" match(s)-->(p:PropertyGroup)");
        StringBuffer cntCypher = new StringBuffer(cypher);
        cntCypher.append(" return count(s) as cnt ");
        cypher.append(" return s,p order by s.updateDate desc, s.name,s.number ").append(this.dealPage(size, index));
        List<InstanceEntityWithCatalog> list = initEntityData(cypher.toString(), map);
        Long cnt = this.excuteCount(cntCypher.toString(), map, "cnt");
        return PageResult.init(cnt.intValue(), index, size, list);
    }

    private List<InstanceEntityWithCatalog> initEntityData(String cypher, Map<String, Object> params) {
        Result run = this.run(cypher, params);
        Record next = null;
        ArrayList list = new ArrayList();

        while(run.hasNext()) {
            next = run.next();
            InstanceEntityWithCatalog row = (InstanceEntityWithCatalog)JSONUtil.parseObject(next.get("s").asNode(), InstanceEntityWithCatalog.class);
            row.setExtensionContent(JSONUtil.parseObject(next.get("p").asNode()));
            list.add(row);
        }

        return list;
    }

    public CustomFolderRepoImpl(){

    }

    //添加文件夹角色容器验证
    private void addFolderValid(StringBuffer cypher, String containerKey){
        cypher.append(" and (not ").append(containerKey).append(".privateFlag or (").append(containerKey).append(")-[:").append(Assign.TYPE).append("]->(:")
                .append(Team.TYPE).append(")-[:").append(Contain.TYPE).append("]->(:")
                .append(TeamRole.TYPE).append(")-[:").append(Contain.TYPE).append("]->(:")
                .append(User.TYPE).append("{oid:$userOid})")
                //包含文件夹角色中的人员可以进入容器
                .append(" or (").append(containerKey).append(")-[:").append(Contain.TYPE).append("*0..]->(:")
                .append(Folder.TYPE).append(")-[:").append(Assign.TYPE).append("]->(:").append(Team.TYPE)
                .append(")-[:").append(Contain.TYPE).append("]->(:")
                .append(TeamRole.TYPE).append(")-[:").append(Contain.TYPE).append("]->(:")
                .append(User.TYPE).append("{oid:$userOid}))");
    }
    @Override
    public List<FolderTreeNode> searchFoldersWithPermisson(String containerType, String containerModel, String containerOid, String searchKey) throws JWIException {
        //searchKey = transfer(searchKey);
        Map<String, Object> params = new HashMap();
        UserDTO currentUser = SessionHelper.getCurrentUser();
        params.put("tenantOid", currentUser.getTenantOid());
        params.put("userOid", currentUser.getOid());

        StringBuffer cypher = new StringBuffer();
        cypher.append("Match (p:").append(containerType).append(")-[")
                .append(dealLabel(Contain.TYPE)).append("]->(f").append(dealLabel(Folder.TYPE)).append(")")
                .append(" where p.tenantOid=$tenantOid ");

        if (StringUtil.isNotBlank(containerModel)) {
            params.put("containerModel", containerModel);
            cypher.append(" and p.modelDefinition=$containerModel ");
        }

        if (StringUtil.isNotBlank(containerOid)) {
            params.put("containerOid", containerOid);
            cypher.append(" and p.oid=$containerOid ");
        }
        if (!(currentUser.isTenantAdmin() || currentUser.isSystemAdmin())) {
            this.addFolderValid(cypher, "p");
        }
        cypher.append(" with p,f Match path=(f)-[r").append(dealLabel(Contain.TYPE))
                .append("*0..]->(s").append(dealLabel(Folder.TYPE)).append(")");
        if (StringUtil.isNotBlank(searchKey)) {
            params.put("searchKey", dealSearchKey(searchKey));
            cypher.append(" where s.name=~$searchKey or s.description=~$searchKey ");
        }
        cypher.append(" return nodes(path) as nodes ");

        Result run = run(cypher.toString(), params);
        return nodes2Tree(run, FolderTreeNode.class);
    }


    @Override
    public List<JSONObject> searchSecData(String folderOid, String searchKey) {
        Map<String,Object> params = new HashMap<>();
        params.put("folderOid",folderOid);
        UserDTO currentUser = SessionHelper.getCurrentUser();
        params.put("tenantOid", currentUser.getTenantOid());
        params.put("userOid", currentUser.getOid());
        params.put("type",Arrays.asList(Folder.TYPE, "MCADDocumentIteration"));
        StringBuffer cypher = new StringBuffer();
        cypher.append("match(m"+dealLabel(Folder.TYPE)).append(")-["+dealLabel(Contain.TYPE))
                .append("]->(n) where m.tenantOid = $tenantOid and n.tenantOid = $tenantOid and m.oid = $folderOid ")
                .append("and n.type in $type return n order by n.createDate desc");
        return excuteList(cypher.toString(),params,"n");

    }

    @Override
    public FolderForExport findFolderTreeForExport(String containerOid) {
        Map<String, Object> params = new HashMap();
        params.put("containerOid", containerOid);
        params.put("tenantOid", SessionHelper.getCurrentUser().getTenantOid());
        StringBuffer cypher = new StringBuffer();
        cypher.append(" match (c:").append(Container.TYPE).append(")-[:").append(Contain.TYPE)
                .append("]->(r:").append(Folder.TYPE).append(") where c.oid = $containerOid ")
                .append(" and c.tenantOid=$tenantOid with r match path=(r)-[:").append(Contain.TYPE)
                .append("*0..]->(l:").append(Folder.TYPE).append(") where not (l)-[:")
                .append(Contain.TYPE).append("]->(:").append(Folder.TYPE).append(") ")
                .append(" return [n in nodes(path) | n{.oid, .name, .description}] as fs ");
        Result run = run(cypher.toString(), params);
        Record next = null;
        Map<String, JSONObject> nodeMap = new HashMap<>();
        List<JSONObject> path = null;
        JSONObject root = null, master = null, temp = null;
        String oid = null;
        while (run.hasNext()) {
            next = run.next();
            path = next.get("fs").asList(item-> JSONUtil.map2JSONObject(item.asMap()));
            if (root == null){
                root = path.get(0);
                nodeMap.put(root.getString("oid"), root);
            }
            master = null;
            for (JSONObject folder : path) {
                oid = folder.getString("oid");
                temp = nodeMap.get(oid);
                if (temp != null){
                    master = temp;
                    continue;
                }
                nodeMap.put(oid, folder);
                MapUtil.putIntoJsonList(master, "children", folder);
                master = folder;
            }

        }
        return root.toJavaObject(FolderForExport.class);
    }

    @Override
    public Folder searchRootFolderByContainerOid(String containerOid, String tenantOid) {
        Map<String, Object> params = new HashMap();
        params.put("tenantOid", tenantOid);
        params.put("containerOid", containerOid);
        StringBuffer cypher = new StringBuffer();
        cypher.append("Match (p:").append(Container.TYPE).append(")-[")
                .append(dealLabel(Contain.TYPE)).append("]->(f").append(dealLabel(Folder.TYPE)).append(")")
                .append(" where p.tenantOid=$tenantOid  and p.oid =$containerOid return f skip 0 limit 1" );
        return (Folder) excuteOne(cypher.toString(),params,"f",Folder.class);
    }

    @Override
    public Folder findFolderByPdmOid(String oid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("oid", oid);
        cypher.append(" match (m:").append(Folder.TYPE).append(")-[:").append(Attribute.TYPE).append("]->(n:")
                .append(PropertyGroup.TYPE).append(") where n.pdmOid=$oid return m ");
        Result run = run(cypher.toString(), map);
        Folder row = null;
        Record next = null;
        while (run.hasNext()) {
            next = run.next();
            row = JSONUtil.parseObject(next.get("m").asNode(), Folder.class);
            //临时添加扩展属性的获取
            Map<String, String> paramsTemp = new HashMap<>();
            paramsTemp.put("oid", row.getOid());
            StringBuffer extensionContentBuf = new StringBuffer();
            extensionContentBuf.append("Match (n").
                    append(entityLabelCypher).append(") where n.oid=$oid").append(" merge (n)-[:ATTRIBUTE]->(p:PropertyGroup) return p");
            Result runTemp = run(extensionContentBuf.toString(), paramsTemp);
            Record nextTemp = null;
            while (runTemp.hasNext()) {
                nextTemp = runTemp.next();
                JSONObject josb = JSONUtil.map2JSONObject(nextTemp.get("p").asNode().asMap());
                row.setExtensionContent(josb);
                break;
            }
        }
        return row;
    }

    @Override
    public Long fuzzyFolderSubCount(Collection<String> folderOids, String searchKey, List<String> subTypes) {
        searchKey = transfer(searchKey);
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("userOid", SessionHelper.getCurrentUser().getOid());

        cypher.append(" call db.index.fulltext.queryNodes('content_management', ")
                .append(" ' (catalogOid:").append(String.join(" OR catalogOid:", folderOids)).append(")' ");

        if (CollectionUtil.isNotEmpty(subTypes)){
            cypher.append(" + ' AND (type:").append(String.join(" OR type:", subTypes)).append(")' ");
        }
        if (StringUtil.isNotBlank(searchKey)) {
            searchKey = "*"+searchKey+"*";
            cypher.append(" + ' AND ( name:").append(searchKey).append(" OR number:").append(searchKey).append(" )' ");
        }
        cypher.append(") yield node as s where s.latest is null or (s.latest=true and ( ")
                .append(" (s.lockOwnerOid is null )")
                .append(" or (s.lockOwnerOid=$userOid and s.lockSourceOid is not null ) ")
                .append(" or (s.lockOwnerOid<>$userOid and s.lockSourceOid is null ))) with s ");
        StringBuffer cntCypher = new StringBuffer(cypher);
        cntCypher.append(" return count(s) as cnt ");

        return this.excuteCount(cntCypher.toString(), map, "cnt");

    }

    public Map<String, Long> fuzzyFolderSubCountMap(Collection<String> folderOids, String searchKey, List<String> subTypes) {
        searchKey = transfer(searchKey);
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("userOid", SessionHelper.getCurrentUser().getOid());
        map.put("oids", folderOids);

        cypher.append("unwind $oids as oid call db.index.fulltext.queryNodes('content_management', ")
                .append(" ' (catalogOid: ' + oid + '").append(")' ");

        if (CollectionUtil.isNotEmpty(subTypes)){
            cypher.append(" + ' AND (type:").append(String.join(" OR type:", subTypes)).append(")' ");
        }
        if (StringUtil.isNotBlank(searchKey)) {
            searchKey = "*"+searchKey+"*";
            cypher.append(" + ' AND ( name:").append(searchKey).append(" OR number:").append(searchKey).append(" )' ");
        }
        cypher.append(") yield node as s where s.latest is null or (s.latest=true and ( ")
                .append(" (s.lockOwnerOid is null )")
                .append(" or (s.lockOwnerOid=$userOid and s.lockSourceOid is not null ) ")
                .append(" or (s.lockOwnerOid<>$userOid and s.lockSourceOid is null ))) ");
        cypher.append(" return oid, count(s) as cnt ");
        Result result = run(cypher.toString(), map);

        Map<String, Long> res = new HashMap<>();
        while (result.hasNext()){
            Record line = result.next();
            String oid = line.get("oid").asString();
            Long count = line.get("cnt").isNull() ? 0 : line.get("cnt").asLong();
            res.put(oid, count);
        }
        return res;
    }

    @Override
    public Folder findFolderByCLS(String clsOid, String containerOid) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("oid", clsOid);
        map.put("containerOid", containerOid);
        cypher.append(" match (f:").append(Folder.TYPE).append(")<-[:").append(Contain.TYPE).append("]-(c:")
                .append(Classification.TYPE).append(") where f.containerOid=$containerOid and c.oid=$oid return f ");
        return  (Folder)this.excuteOne(cypher.toString(), map, "f", Folder.class);
    }



    @Override
    public List<Folder> searchRootFolder(String containerType, String searchKey) {
        Map<String, Object> params = new HashMap();
        UserDTO currentUser = SessionHelper.getCurrentUser();
        params.put("tenantOid", currentUser.getTenantOid());
        params.put("userOid", currentUser.getOid());

        StringBuffer cypher = new StringBuffer();
        cypher.append("Match (p:").append(containerType).append(")-[")
                .append(dealLabel(Contain.TYPE)).append("]->(f").append(dealLabel(Folder.TYPE)).append(")")
                .append(" where p.tenantOid=$tenantOid ");
        if (!(currentUser.isTenantAdmin() || currentUser.isSystemAdmin())) {
            this.addFolderValid(cypher, "p");
        }
        if (StringUtil.isNotBlank(searchKey)){
            params.put("searchKey", dealSearchKey(searchKey));
            cypher.append(" where s.name=~$searchKey or s.description=~$searchKey ");
        }
        cypher.append(" return f order by f.createDate desc");
        return excuteList(cypher.toString(),params,"f",Folder.class);

    }

    @Override
    public List<FolderTreeNode> searchFolders(String containerType, String containerOid, String searchKey) throws JWIException {
        Map<String, Object> params = new HashMap();
        UserDTO currentUser = SessionHelper.getCurrentUser();
        params.put("tenantOid", currentUser.getTenantOid());
        params.put("userOid", currentUser.getOid());

        StringBuffer cypher = new StringBuffer();
        cypher.append("Match (p:").append(containerType).append(")-[")
                .append(dealLabel(Contain.TYPE)).append("]->(f").append(dealLabel(Folder.TYPE)).append(")")
                .append(" where p.tenantOid=$tenantOid ");
        if (StringUtil.isNotBlank(containerOid)) {
            params.put("containerOid", containerOid);
            cypher.append(" and p.oid=$containerOid ");
        }
        cypher.append(" with f Match path=(f)-[r").append(dealLabel(Contain.TYPE))
                .append("*0..]->(s").append(dealLabel(Folder.TYPE)).append(")");
        if (StringUtil.isNotBlank(searchKey)){
            params.put("searchKey", dealSearchKey(searchKey));
            cypher.append(" where s.name=~$searchKey or s.description=~$searchKey ");
        }
        cypher.append(" return nodes(path) as nodes ");

        Result run = run(cypher.toString(), params);
        return nodes2Tree(run, FolderTreeNode.class);
    }

    @Override
    public Long deleteFolder(String oid) throws JWIException {
        Map<String, Object> params = new HashMap();
        params.put("oid", oid);

        StringBuffer cypher = new StringBuffer();
        cypher.append("Match (f").append(dealLabel(Folder.TYPE)).append(" {oid:$oid})")
                .append(" detach delete f return count(f) as cnt ");
        return excuteCount(cypher.toString(), params, "cnt");
    }

    @Override
    public boolean hasContain(String oid) {
        Map<String, Object> params = new HashMap();
        params.put("oid", oid);

        StringBuffer cypher = new StringBuffer();
        cypher.append("Match (f").append(dealLabel(Folder.TYPE)).append(" {oid:$oid}) ")
                .append(" where (f)-[:").append(Contain.TYPE).append("]->() ")
                .append(" return count(f)>0 as flag ");
        return excuteBoolean(cypher.toString(), params, "flag");
    }

    @Override
    public List<Folder> findByOids(List<String> oids) {
        Map<String, Object> params = new HashMap();
        params.put("oids", oids);
        StringBuffer cypher = new StringBuffer();
        cypher.append("unwind $oids as oid match(f").append(dealLabel(Folder.TYPE)).append(") ")
                .append("where f.oid = oid ")
                .append("optional match (f)-[:ATTRIBUTE]->(pg:PropertyGroup) return f, pg ");

        Result run = this.run(cypher.toString(), params);
        Record next = null;
        List<Folder> result = new ArrayList<>();
        Folder row = null;
        Class<Folder> clazz = Folder.class;
        while (run.hasNext()) {
            next = run.next();
            row = JSONUtil.parseObject(next.get("f"), clazz);
            JSONObject extensionContent = Optional.ofNullable(JSONUtil.toJSON(next.get("pg"))).orElse(new JSONObject());
            row.setExtensionContent(extensionContent);
            result.add(row);
        }
        return result;
    }

    @Override
    public PageResult<InstanceEntity> dynamicQuerySubPage(SubPageFilter subPageFilter) throws JWIException {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        String fromType = subPageFilter.getFromType();
        String fromOid = subPageFilter.getFromOid();
        String relationType = subPageFilter.getType();
        String toType = subPageFilter.getToType();
        Condition secFilter = subPageFilter.getFilter();
        Order order = subPageFilter.getOrder();
        int index = subPageFilter.getIndex();
        int size = subPageFilter.getSize();
        map.put("fromOid", fromOid);
        cypher.append(" Match (m").append(dealLabel(fromType)).append(")-[").append(dealLabel(relationType)).append("]->(s").append(dealLabel(toType)).append(") where m.oid=$fromOid ");
        if (secFilter != null) {
            cypher.append(" and ").append(secFilter.parse(new ConditionParserNeo4j("s", map)));
        }

        StringBuffer cntCypher = new StringBuffer(cypher);
        cypher.append(" return s ").append(order.parse(new OrderParserNeo4j("s"))).append(this.dealPage(size, index));
        cntCypher.append(" return count(s) as cnt ");
        List<InstanceEntity> list = excuteList(cypher.toString(), map, "s", InstanceEntity.class);
        Long cnt = this.excuteCount(cntCypher.toString(), map, "cnt");
        return PageResult.init(cnt.intValue(), index, size, list);
    }

    @Override
    public boolean refreshRootFolderNameByContainer(String containerType, String containerOid, String name) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap<>();
        map.put("containerOid", containerOid);
        map.put("name", name);
        cypher.append(" match (m:").append(containerType).append(")-[:").append(Contain.TYPE).append("]->(s:")
                .append(Folder.TYPE).append(") where m.oid=$containerOid set s.name=$name ");
        excute(cypher.toString(), map);
        return true;
    }

    @Override
    public List<Folder> flapFolderTree(String folderOid){
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("folderOid", folderOid);
        cypher.append(" Match (m:").append(Folder.TYPE).append("{oid:$folderOid}) with m match (f:").append(Folder.TYPE).
                append(") where f.containerOid=m.containerOid and (f)<-[:").append(Contain.TYPE).append("*0..]-(m) ")
                .append("return f ");
        return excuteList(cypher.toString(), map, "f", Folder.class);
    }

    public PageResult<InstanceEntityWithCatalog> fuzzyFolderContentPage(FuzzyPageVO pageVO) {
        Collection<String> folderOids = pageVO.getFolderOids();
        String searchKey = pageVO.getSearchKey();
        Integer index = pageVO.getIndex();
        Integer size = pageVO.getSize();
        List<String> subTypes = pageVO.getSubTypes();
        List<String> models = pageVO.getModels();
        String preferenceLevel = pageVO.getPreferenceLevel();
        String supplierOid = pageVO.getSupplierOid();
        List<String> oidList = pageVO.getOidList();
        
        searchKey = transfer(searchKey);
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap();
        map.put("userOid", SessionHelper.getCurrentUser().getOid());

        List<String[]> folderArr = splitArrayByCount(folderOids.toArray(new String[0]), 1000);
        //处理超出1024个搜索的问题
        cypher.append(" call db.index.fulltext.queryNodes('content_management', '(");

        for (int i = 0; i < folderArr.size(); i++) {
            String[] subFolders = folderArr.get(i);
            cypher.append("(catalogOid:").append(String.join(" OR ", subFolders)).append(" ) ");
            if(i != folderArr.size() - 1){
                cypher.append(" OR ");
            }
        }

        cypher.append(") ");

        if (CollectionUtil.isNotEmpty(subTypes)){
            cypher.append(" AND (type:").append(String.join(" OR type:", subTypes)).append(") ");
        }
        if (StringUtil.isNotBlank(searchKey)) {
            searchKey = "*"+searchKey+"*";
            cypher.append(" AND ( name:").append(searchKey).append(" OR number:").append(searchKey).append(" ) ");
        }
        cypher.append("') yield node as s where s.latest is null or (s.latest=true and ( ")
                .append(" (s.lockOwnerOid is null )")
                .append(" or (s.lockOwnerOid=$userOid and s.lockSourceOid is not null ) ")
                .append(" or (s.lockOwnerOid<>$userOid and s.lockSourceOid is null ))) ");
        if(CollectionUtil.isNotEmpty(oidList)){
            cypher.append(" and s.oid in $oidList");
            map.put("oidList",oidList);
        }
        if (CollectionUtil.isNotEmpty(models)){
            cypher.append(" and s.modelDefinition in $models");
            map.put("models",models);
        }
        if (StringUtil.isNotBlank(preferenceLevel)) {
            map.put("preferenceLevel", preferenceLevel);
            cypher.append(" AND ( s.preferenceLevel=$preferenceLevel ) ");
        }
        if (StringUtil.isNotBlank(supplierOid)) {
            map.put("supplierOid", supplierOid);
            cypher.append(" with s match (s)<-[it:ITERATE]-(m)-[r:CONTAIN]->(su:SupplierPart) where su.manufacturer=$supplierOid  ");
        }

        cypher.append(" with s");
        StringBuffer cntCypher = new StringBuffer(cypher);
        cntCypher.append(" return count(s) as cnt ");
        cypher.append(" with s optional match (s)-[").append(dealLabel(Attribute.TYPE)).append("]->(pro").append(dealLabel(PropertyGroup.TYPE)).append(")");
        cypher.append(" return s,pro order by s.updateDate desc, s.name,s.number ");
        if(size!=0) {
           cypher.append(dealPage(size, index));
        }
        Result run = this.run(cypher.toString(), map);
        Record next = null;
        List<InstanceEntityWithCatalog> result = new ArrayList<>();
        InstanceEntityWithCatalog row = null;
        Class<InstanceEntityWithCatalog> clazz = InstanceEntityWithCatalog.class;
        while (run.hasNext()) {
            next = run.next();
            row = JSONUtil.parseObject(next.get("s"), clazz);
            JSONObject extensionContent = Optional.ofNullable(JSONUtil.toJSON(next.get("pro"))).orElse(new JSONObject());
            row.setExtensionContent(extensionContent);
            result.add(row);
        }
        Long cnt = this.excuteCount(cntCypher.toString(), map, "cnt");
        return PageResult.init(cnt.intValue(), index, size, result);
    }

    /**
     * 根据数量分割数组
     * @param array
     * @param chunkSize
     * @return
     */
    public static List<String[]> splitArrayByCount(String[] array, int chunkSize) {
        return IntStream.range(0, (array.length + chunkSize - 1) / chunkSize)
                .mapToObj(i -> Arrays.copyOfRange(array, i * chunkSize, Math.min((i + 1) * chunkSize, array.length)))
                .collect(Collectors.toList());
    }


    @Override
    public Folder rename(String oid, String name) {
        StringBuffer cypher = new StringBuffer();
        Map<String, Object> map = new HashMap<>();
        map.put("oid", oid);
        map.put("name", name);
        cypher.append(" match (m:").append(Folder.TYPE).append(") where m.oid=$oid set m.name=$name return m ");
        return (Folder)excuteOne(cypher.toString(), map, "m", Folder.class);
    }

    private final static String REG_EX = "['/`\"~!:@#$%^*()+|{}\\[\\]<>/？?！（）【】‘；：”“’。，、\\\\]";

    private String transfer(String searchKey){
        if (StringUtil.isNotBlank(searchKey)) {
           Matcher m = Pattern.compile(REG_EX).matcher(searchKey);
           if (m.find()) {
               String[] symbolList = {"\\", "$", "(", ")", "*", "+", "[", "]",
                       "?", "^", "{", "}", "|","'","!","~",":","\"" ,"/"};
               String[] symbolArr1 = {"'","\\"};
               List<String> symbolList1 = Arrays.asList(symbolArr1);
               for (String symbol : symbolList) {
                   if(symbolList1.contains(symbol)){
                       searchKey = searchKey.replace(symbol, "\\" + symbol);
                   }else if (searchKey.contains(symbol)) {
                       searchKey = searchKey.replace(symbol, "\\\\" + symbol);
                   }
               }
           }
        }
        return searchKey;
    }

}
